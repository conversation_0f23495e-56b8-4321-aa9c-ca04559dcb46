<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>$(NetVersion);$(NetVersion)-android;$(NetVersion)-ios;$(NetVersion)-maccatalyst</TargetFrameworks>
    <TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);$(NetVersion)-windows10.0.19041.0</TargetFrameworks>
    <TargetFrameworks Condition="'$(IncludeTizenTargetFrameworks)' == 'true'">$(TargetFrameworks);$(NetVersion)-tizen</TargetFrameworks>
    <SingleProject>true</SingleProject>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <IsAotCompatible>true</IsAotCompatible>

    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>

    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
    <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'">6.5</SupportedOSPlatformVersion>
  </PropertyGroup>

  <PropertyGroup>
    <PackageId>AppoMobi.Preview.CommunityToolkit.Maui.Core</PackageId>
    <Authors>Microsoft and contributors</Authors>
    <NeutralLanguage>en</NeutralLanguage>
    <Copyright>© Microsoft Corporation and contributors.</Copyright>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <DefineConstants>$(DefineConstants);</DefineConstants>
    <UseFullSemVerForNuGet>false</UseFullSemVerForNuGet>
    <Title>Modified CommunityToolkit.Maui.Core for enhanced POPUPS</Title>
    <Description>Core library for community toolkits using .NET MAUI</Description>
    <PackageIcon>icon.png</PackageIcon>
    <Product>$(AssemblyName) ($(TargetFramework))</Product>
    <Version>1.1.2-pre1</Version>
    <PackageVersion>$(Version)$(VersionSuffix)</PackageVersion>
    <PackageRequireLicenseAcceptance>true</PackageRequireLicenseAcceptance>
    <PackageTags>dotnet,maui,toolkit,kit,communitytoolkit,dotnetcommunitytoolkit</PackageTags>
    <Configurations>Debug;Release</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <None Include="..\..\build\nuget.png" PackagePath="icon.png" Pack="true" />
    <None Include="ReadMe.txt" pack="true" PackagePath="." />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Maui.Core" Version="[$(MauiPackageVersion),$(NextMauiPackageVersion))"/>
    <PackageReference Include="Microsoft.Maui.Essentials" Version="[$(MauiPackageVersion),$(NextMauiPackageVersion))"/>
    <PackageReference Include="Microsoft.SourceLink.GitHub" Version="8.0.0" Condition=" '$(Configuration)'=='Release' " PrivateAssets="All" />
    <PackageReference Include="System.Speech" Version="9.0.0" Condition="'$(TargetFramework)' == '$(NetVersion)-windows10.0.19041.0'" />
  </ItemGroup>

</Project>
