﻿using AppoMobi.Forms.Controls.Skia;
using SkiaSharp;
using SkiaSharp.Views.Maui;
using SkiaSharp.Views.Maui.Controls;
 


using BitmapStretch = AppoMobi.Forms.Controls.Skia.BitmapStretch;
using SkiaImage = AppoMobi.Forms.Controls.Skia.SkiaImage;

namespace AppoMobi.Forms.Content.Camera.Controls
{
	public class ViewportFrames : SKCanvasView, IDisposable
	{
		//-------------------------------------------------------------
		// FontFamily
		//-------------------------------------------------------------
		private const string nameFontFamily = "FontFamily";
		public static readonly BindableProperty FontFamilyProperty = BindableProperty.Create(nameFontFamily, typeof(string), typeof(ViewportFrames), null,
			propertyChanged: RedrawCanvas);
		public string FontFamily
		{
			get { return (string)GetValue(FontFamilyProperty); }
			set
			{
				SetValue(FontFamilyProperty, value);
			}
		}

		void UpdateFont()
		{
            //_typeface = CustomFontManager.Instance.GetFont(FontFamily);
            MainThread.BeginInvokeOnMainThread(() =>
            {
                InvalidateSurface();
            });
		}

		public ViewportFrames()
		{
			//IgnorePixelScaling = true;

			HorizontalOptions = LayoutOptions.Start;
			VerticalOptions = LayoutOptions.Start;
			BackgroundColor = Colors.Transparent;

			UpdateFont();

			//SKFontManager.Default.MatchCharacter('м');

			PaintSurface += CanvasViewOnPaintSurface;

			SizeChanged += OnSizeChanged;
		}

		private void OnSizeChanged(object sender, EventArgs e)
		{
			Update();
		}

		public void Dispose()
		{
			PaintSurface -= CanvasViewOnPaintSurface;
		}

		#region RENDERING

		//private static float _scaleResampleText = 3.0f;

		public float Density { get; set; }

		/// <summary>
		/// Draw focal frames over passed canvas,
		/// we use scale so we can draw them over something of different size than the real view,
		/// like for drawing over virtual canvas.
		/// </summary>
		/// <param name="info"></param>
		/// <param name="canvas"></param>
		/// <param name="scaleX"></param>
		/// <param name="scaleY"></param>
		public void RenderFrames(SKImageInfo info, SKCanvas canvas, double scaleX = 1.0, double scaleY = 1.0, double sensor = 0)
		{



			//var outerFrameWidth = (float)(OuterFrameWidth * scaleX);
			//var outerFrameHeight = (float)(OuterFrameHeight * scaleY);


			//todo frames here

			if (ItemsSource != null && ItemsSource.Count > 0)
			{
				var frameStrokeWidth = (float)(1.0f * scaleX);
				int textMargin = (int)(2 * scaleX);

				//var typeface = SKFontManager.Default.MatchCharacter('м');

				using (var framePaint = new SKPaint
				{
					Style = SKPaintStyle.Stroke,
					StrokeWidth = frameStrokeWidth,
					StrokeCap = SKStrokeCap.Square,
					Color = FramesColor.ToSKColor()
				})
				using (var framePaintBorder = new SKPaint
				{
					Style = SKPaintStyle.Stroke,
					StrokeWidth = frameStrokeWidth,
					Color = SKColor.Parse("#33000000"),
					StrokeCap = SKStrokeCap.Square,
					//PathEffect = SKPathEffect.CreateDash(new float[]{ 0, 20, 20, 20 }, 20)
				})
				using (var textPaint = new SKPaint
				{
					TextSize = (float)(12 * scaleX),
					Color = AnnotationColor.ToSKColor(),
					Typeface = this.TypeFace,
					IsAntialias = true
				})
				using (var textStroke = new SKPaint
				{
					TextSize = (float)(12 * scaleX),
					Color = ColorOutline,
					StrokeWidth = 4.5f,
					IsStroke = true,
					IsAntialias = true,
					Typeface = TypeFace,
				})
				{
					foreach (var frame in ItemsSource)
					{

						//frame size and position
						var frameWidth = (float)(frame.FrameWidth * scaleX);
						var frameHeight = (float)(frame.FrameHeight * scaleY);
						var frameX = (float)(info.Width - frameWidth) / 2.0f;
						var frameY = (float)(info.Height - frameHeight) / 2.0f;


						#region FOG OF WAR

						if (OuterFrameId == frame.Id && FogEnabled)
						{
							canvas.Save();

							var clipWidth = frameWidth - framePaint.StrokeWidth;
							if (clipWidth >= info.Width)
								clipWidth = info.Width;

							var clipHeight = frameHeight - framePaint.StrokeWidth;
							if (clipHeight > info.Height)
								clipHeight = info.Height;

							//create clip path
							var clipRect = new SKRect(0, 0, clipWidth, clipHeight);

							//center inside canvas
							var offsetX = (info.Width - clipRect.Width) / 2.0f;
							if (offsetX < 0)
								offsetX = 0;

							var offsetY = (info.Height - clipRect.Height) / 2.0f;
							if (offsetY < 0)
								offsetY = 0;

							//var clipRectCentered = new new SKRect(0, 0, (float)ClipWidth, (float)ClipHeight);

							clipRect.Offset(new SKPoint(offsetX, offsetY));

							if (clipRect.Left == 0)
								clipRect.Left = 0.1f;

							if (clipRect.Right == info.Width)
								clipRect.Right = info.Width - 0.1f;

							//clip
							var clipPath = new SKPath();
							clipPath.AddRect(clipRect);
							canvas.ClipPath(clipPath, SKClipOperation.Difference, false);

							//create filler
							var paint = new SKPaint
							{
								Style = SKPaintStyle.StrokeAndFill,
								Color = FogColor.ToSKColor()
							};

							// draw fill
							canvas.DrawRect(0, 0, info.Width, info.Height, paint);

							//Debug.WriteLine($"[FOG] {info.Width}x{info.Height} clipped l:{clipRect.Left} t:{clipRect.Top} r:{clipRect.Right} b:{clipRect.Bottom}");

							canvas.Restore();
						}


						#endregion

						var annotation = $"{frame.Preset.FocalDistance} mm";
						var textHeight = textPaint.TextSize;// fm.Bottom - fm.Top;//                        var textHeight = fm.Descent - fm.Ascent;
						var textWidth = textPaint.MeasureText(annotation);

						var textX = frameX + frameWidth - textWidth - frameStrokeWidth - textMargin;
						var textY = frameY + frameHeight - frameStrokeWidth - textMargin; //bottom inside

						if (sensor != 0)
						{
							if (sensor == 90.0) //to the left
							{
								textX = frameX - textWidth / 2 - textHeight / 2.0f + frameStrokeWidth + textMargin;
								textY = frameY + frameHeight - textWidth / 2.0f - textHeight / 2.0f - frameStrokeWidth - textMargin;
							}
							else
							if (sensor == 270.0) //to the right
							{
								textX = frameX + frameWidth - textWidth / 2.0f + textHeight / 2.0f - frameStrokeWidth - textMargin;
								textY = frameY + textWidth / 2 - textHeight / 2.0f + frameStrokeWidth + textMargin;
							}
							else
							if (sensor == 180) //upside down
							{
								textX = frameX + frameStrokeWidth + textMargin;
								textY = frameY - frameStrokeWidth - textMargin;
							}
						}

						//frame blackened
						canvas.DrawRect(frameX + frameStrokeWidth, frameY + frameStrokeWidth,
							frameWidth, frameHeight,
							framePaintBorder);

						//frame 
						canvas.DrawRect(frameX, frameY,
							frameWidth, frameHeight,
							framePaint);

						//    canvas.DrawRect(frameX + framePaint.StrokeWidth * 2, frameY + framePaint.StrokeWidth * 2, frameWidth - framePaint.StrokeWidth * 2 * 2, frameHeight - framePaint.StrokeWidth * 2 * 2, framePaintBorder);

						//frame fits inside screen
						//  canvas.DrawRect(frameX + framePaint.StrokeWidth, frameY + framePaint.StrokeWidth, frameWidth - framePaint.StrokeWidth * 2, frameHeight - framePaint.StrokeWidth * 2, framePaint);

						//draw annotaion
						if (sensor != 0)
						{
							canvas.Save();
							canvas.RotateDegrees((float)sensor, textX + textWidth / 2.0f, textY + textHeight / 2.0f);

							canvas.DrawText(annotation, textX, textY, textStroke);
							canvas.DrawText(annotation, textX, textY, textPaint);

							//   SkiaLabel.DrawRasterizedText(canvas, textX - 4, textY, annotation, textPaint, ColorOutline);

							canvas.Restore();
						}
						else
						{
							canvas.DrawText(annotation, textX, textY, textStroke);
							canvas.DrawText(annotation, textX, textY, textPaint);

							//   canvas.DrawText(annotation, textX, textY, textStroke);

							//SkiaLabel.DrawRasterizedText(canvas, textX - 4, textY, annotation, textPaint, ColorOutline);

							//using (var imageText = SkiaLabel.RasterizeText( annotation, textPaint, ColorOutline))
							//{
							//    canvas.DrawImage(imageText, (float)textX, (float)textY - imageText.Height + 1);
							//}
							//canvas.DrawText(annotation, textX, textY, textPaint);
						}

					}
				}
			}

			//render more info
			if (RenderOverlay != null)
			{
				RenderOverlay.Invoke(info, canvas, scaleX, sensor);
			}
		}

		public static SKColor ColorOutline = Color.Parse("#99000000").ToSKColor();

		public Action<SKImageInfo, SKCanvas, double, double> RenderOverlay;

		#endregion

		#region OUTPUT FILE


		public static void ExecuteWithPlatformFix(Action action)
		{
			if (DeviceInfo.Current.Platform == DevicePlatform.iOS)
			{
				MainThread.BeginInvokeOnMainThread(action);
			}
			else
			{
				action();
			}
		}

		public async Task<Stream> RenderOverPhoto(SKBitmap bitmap,
		double rescaleCanvasX, double rescaleCanvasY, int previewWidth, int previewHeight,
		double zoomCapturedPhotoX = 1.0, double zoomCapturedPhotoY = 1.0, double sensorRotation = 0.0)
		{
			var width = Width;
			var height = Height;

			var scaleX = rescaleCanvasX;
			var scaleY = rescaleCanvasY;

			if (sensorRotation == 90 || sensorRotation == 270)
			{
				//height = Width;
				//width = Height;
				scaleX = rescaleCanvasY;
				scaleY = rescaleCanvasX;
			}

			var w = (int)(width * scaleX);
			var h = (int)(height * scaleY);

			var info = new SKImageInfo(w, h);

			SkiaImage image = null;

			using (var surface = SKSurface.Create(info))
			{
				if (surface == null)
				{
					Console.WriteLine($"Cannot create SKSurface {w}x{h}");
					return null;
				}

				//create canvas of my own size * scale
				SKCanvas canvas = surface.Canvas;

				canvas.Clear(SKColors.Gray);

				var check = SensorRotation;

				//render image
				image = new SkiaImage
				{
					SensorRotation = sensorRotation,
					HorizontalAlignment = BitmapAlignment.Center,
					VerticalAlignment = BitmapAlignment.Center,
					Aspect = BitmapStretch.AspectFill,
					ZoomX = zoomCapturedPhotoX,
					ZoomY = zoomCapturedPhotoY,
					Bitmap = bitmap
				};

				bool isRendering = true;
				bool error = true;

				image.RenderImage(info, canvas);

				await Task.Delay(50); //render image
									  //apply frames and fog
				RenderFrames(info, canvas, scaleX, scaleY, sensorRotation);

				image?.Dispose();

				try
				{
					return ToStream(surface);
				}
				catch (Exception e)
				{
					Console.WriteLine(e);
					return null;
				}

			}

		}


		public async Task<Stream> RenderOverPhoto(string filenameInput,
			double rescaleCanvasX, double rescaleCanvasY, int previewWidth, int previewHeight,
			double zoomCapturedPhotoX = 1.0, double zoomCapturedPhotoY = 1.0, double sensorRotation = 0.0)
		{
			var width = Width;
			var height = Height;

			var scaleX = rescaleCanvasX;
			var scaleY = rescaleCanvasY;

			if (sensorRotation == 90 || sensorRotation == 270)
			{
				//height = Width;
				//width = Height;
				scaleX = rescaleCanvasY;
				scaleY = rescaleCanvasX;
			}


			var w = (int)(width * scaleX);
			var h = (int)(height * scaleY);

			var info = new SKImageInfo(w, h);

			SkiaImage image = null;

			using (var surface = SKSurface.Create(info))
			{
				if (surface == null)
				{
					Console.WriteLine($"Cannot create SKSurface {w}x{h}");
					return null;
				}

				//create canvas of my own size * scale
				SKCanvas canvas = surface.Canvas;

				canvas.Clear(SKColors.Gray);

				var check = SensorRotation;

				//render image
				image = new SkiaImage
				{
					SensorRotation = sensorRotation,
					HorizontalAlignment = BitmapAlignment.Center,
					VerticalAlignment = BitmapAlignment.Center,
					Aspect = BitmapStretch.AspectFill,
					ZoomX = zoomCapturedPhotoX,
					ZoomY = zoomCapturedPhotoY
				};

				bool isRendering = true;
				bool error = true;

				image.OnSuccess += (sender, args) =>
				{
					error = false;
				};

				//image.OnError += (sender, args) =>
				//{
				//    error = true;

				//};

				image.OnFinishedLoading += (sender, args) =>
				{
					isRendering = false;
				};

				await Task.Delay(100); //this is a shitty fricking fix for the saved file not being ready yet for read

				//if (DeviceInfo.Current.Platform == DevicePlatform.Android)
				//{
				//    await Task.Delay(100); //this is a shitty fricking fix for the saved file not being ready yet for read
				//}

				image.ImageSource = filenameInput;

				while (isRendering)
				{
					await Task.Delay(10);
				}

				if (!error)
				{
					image.RenderImage(info, canvas);

					//apply frames and fog
					RenderFrames(info, canvas, scaleX, scaleY, sensorRotation);
				}

				image?.Dispose();

				try
				{
					return ToStream(surface);
				}
				catch (Exception e)
				{
					Console.WriteLine(e);
					return null;
				}

			}

		}


		public Stream ToStream(SKSurface surface, SKEncodedImageFormat format = SKEncodedImageFormat.Jpeg, int quality = 90)
		{
			try
			{

				using (var image = surface.Snapshot())
				{
					var bitmap = SKBitmap.FromImage(image);
					var rotated = Reorient(bitmap);

					var data = rotated.Encode(format, quality);
					return data.AsStream();
				}

				SKBitmap Reorient(SKBitmap bitmap)
				{
					SKBitmap rotated;
					switch (SensorRotation)
					{
						case 180:
							using (var surface = new SKCanvas(bitmap))
							{
								surface.RotateDegrees(180, bitmap.Width / 2.0f, bitmap.Height / 2.0f);
								surface.DrawBitmap(bitmap.Copy(), 0, 0);
							}
							return bitmap;
						case 270:
							rotated = new SKBitmap(bitmap.Height, bitmap.Width);
							using (var surface = new SKCanvas(rotated))
							{
								surface.Translate(rotated.Width, 0);
								surface.RotateDegrees(90);
								surface.DrawBitmap(bitmap, 0, 0);
							}
							return rotated;
						case 90:
							rotated = new SKBitmap(bitmap.Height, bitmap.Width);
							using (var surface = new SKCanvas(rotated))
							{
								surface.Translate(0, rotated.Height);
								surface.RotateDegrees(270);
								surface.DrawBitmap(bitmap, 0, 0);
							}
							return rotated;
						default:
							return bitmap;
					}
				}
			}
			catch (Exception e)
			{
				Console.WriteLine(e);
				return null;
			}
		}

		private bool _SavingToFile;

		#endregion


		private object _lockUpdateViewport = new object();

		public void UpdateViewport(double width, double height, double cameraFocal)
		{
			lock (_lockUpdateViewport)
			{
				if (width > 1 && height > 1 && cameraFocal > 0)
				{
					if (ItemsSource != null && ItemsSource.Count > 0)
					{
						var outerWidth = 0.0;
						var outerHeight = 0.0;
						string outerId = null;

						var let = 1 * Density;

						foreach (var frame in ItemsSource)
						{
							//pass data to draw frame
							frame.UpdateViewport(width, height, cameraFocal);

							//Debug.WriteLine($"[CHECK] {width}x{height} - frame:{frame.FrameWidth}x{frame.FrameHeight}");

							//define which frame is considered active, fog others outside
							if (frame.FrameWidth - let <= width && frame.FrameHeight - let <= height
																&& (frame.FrameHeight > outerHeight || frame.FrameWidth > outerWidth))
							{
								outerWidth = frame.FrameWidth;
								outerHeight = frame.FrameHeight;
								outerId = frame.Id;
							}


						}

						//for last frame case
						if (outerWidth == 0.0 && outerHeight == 0.0 && ItemsSource.Any())
						{
							var max = ItemsSource.Min(m => m.FrameWidth);
							var frame = ItemsSource.FirstOrDefault(x => x.FrameWidth == max);
							if (frame != null)
							{
								outerId = frame.Id;
							}
						}

						OuterFrameId = outerId;

						InvalidateSurface();

						if (!initialized)
						{
							initialized = true;
							OnInitialized?.Invoke(this, null);
						}
					}
					else
					{
						InvalidateSurface(); //delete all if empty
					}
				}
			}
		}

		private bool initialized;

		//private double _OuterFrameWidth;
		//public double OuterFrameWidth
		//{
		//	get { return _OuterFrameWidth; }
		//	set
		//	{
		//		if (_OuterFrameWidth != value)
		//		{
		//			_OuterFrameWidth = value;
		//			OnPropertyChanged();
		//		}
		//	}
		//}

		//private double _OuterFrameHeight;
		//public double OuterFrameHeight
		//{
		//	get { return _OuterFrameHeight; }
		//	set
		//	{
		//		if (_OuterFrameHeight != value)
		//		{
		//			_OuterFrameHeight = value;
		//			OnPropertyChanged();
		//		}
		//	}
		//}

		private string _OuterFrameId;
		public string OuterFrameId
		{
			get
			{
				return _OuterFrameId;
			}
			set
			{
				if (_OuterFrameId != value)
				{
					_OuterFrameId = value;
					OnPropertyChanged();
				}
			}
		}



		/// <summary>
		/// DRAW!
		/// </summary>
		/// <param name="sender"></param>
		/// <param name="argsSurface"></param>
		private void CanvasViewOnPaintSurface(object sender, SKPaintSurfaceEventArgs argsSurface)
		{
			SKCanvas canvas = argsSurface.Surface.Canvas;

			canvas.Clear(SKColors.Transparent);

			SKImageInfo info = argsSurface.Info;

			if (Density == 0)
				Density = (float)(info.Width / this.Width);

			//var svgClipShape = "<svg><rect width= \"20\" height= \"20\" style =\"fill:rgb(0,0,255);\" /></svg>";
			//SKPath pathClip = SKPath.ParseSvgPathData(svgClipShape);

			canvas.Clear(SKColors.Transparent);

			RenderFrames(info, canvas, Density, Density, SensorRotation);

			//if (_SavingToFile)
			//{
			//    _SavingToFile = false;
			//    OutputToFile(argsSurface.Surface, _fullAbsoluteFilenameWithPath);
			//}

		}


		#region PROPERTIES

		//todo ElementPosition AnnotationPosition

		
		// ItemsSource
		

		private const string nameItemsSource = "ItemsSource";
		public static readonly BindableProperty ItemsSourceProperty = BindableProperty.Create(nameItemsSource, typeof(IList<PresetViewport>), typeof(ViewportFrames), null,
			propertyChanged: RedrawCanvas);
		public IList<PresetViewport> ItemsSource
		{
			get { return (IList<PresetViewport>)GetValue(ItemsSourceProperty); }
			set { SetValue(ItemsSourceProperty, value); }
		}

		//-------------------------------------------------------------
		// ViewportFocalLength
		//-------------------------------------------------------------
		private const string nameViewportFocalLength = "ViewportFocalLength";
		public static readonly BindableProperty ViewportFocalLengthProperty = BindableProperty.Create(nameViewportFocalLength, typeof(double), typeof(ViewportFrames),
			0.0);
		public double ViewportFocalLength
		{
			get { return (double)GetValue(ViewportFocalLengthProperty); }
			set { SetValue(ViewportFocalLengthProperty, value); }
		}

		//-------------------------------------------------------------
		// SensorRotation
		//-------------------------------------------------------------
		private const string nameSensorRotation = "SensorRotation";
		public static readonly BindableProperty SensorRotationProperty = BindableProperty.Create(nameSensorRotation, typeof(double), typeof(ViewportFrames),
			0.0);
		public double SensorRotation
		{
			get { return (double)GetValue(SensorRotationProperty); }
			set { SetValue(SensorRotationProperty, value); }
		}

		/*
        //-------------------------------------------------------------
        // ClipWidth
        //-------------------------------------------------------------
        private const string nameClipWidth = "ClipWidth";
        public static readonly BindableProperty ClipWidthProperty = BindableProperty.Create(nameClipWidth, typeof(double), typeof(ViewportFrames),
            40.0,
            propertyChanged: RedrawCanvas);
        public double ClipWidth
        {
            get { return (double)GetValue(ClipWidthProperty); }
            set { SetValue(ClipWidthProperty, value); }
        }

        //-------------------------------------------------------------
        // ClipHeight
        //-------------------------------------------------------------
        private const string nameClipHeight = "ClipHeight";
        public static readonly BindableProperty ClipHeightProperty = BindableProperty.Create(nameClipHeight, typeof(double), typeof(ViewportFrames),
            40.0,
            propertyChanged: RedrawCanvas);
        public double ClipHeight
        {
            get { return (double)GetValue(ClipHeightProperty); }
            set { SetValue(ClipHeightProperty, value); }
        }
        */

		//-------------------------------------------------------------
		// FogColor
		//-------------------------------------------------------------
		private const string nameFogColor = "FogColor";
		public static readonly BindableProperty FogColorProperty = BindableProperty.Create(nameFogColor, typeof(Color), typeof(ViewportFrames),
			Colors.DarkGray,
			propertyChanged: RedrawCanvas); //, BindingMode.TwoWay
		/// <summary>
		/// Color of the fog masking screen over the outer frame
		/// </summary>
		public Color FogColor
		{
			get { return (Color)GetValue(FogColorProperty); }
			set { SetValue(FogColorProperty, value); }
		}


		//-------------------------------------------------------------
		// FogEnabled
		//-------------------------------------------------------------
		private const string nameFogEnabled = "FogEnabled";
		public static readonly BindableProperty FogEnabledProperty = BindableProperty.Create(nameFogEnabled, typeof(bool), typeof(ViewportFrames), true,
			propertyChanged: RedrawCanvas); //, BindingMode.TwoWay
		public bool FogEnabled
		{
			get { return (bool)GetValue(FogEnabledProperty); }
			set { SetValue(FogEnabledProperty, value); }
		}

		//-------------------------------------------------------------
		// FramesColor
		//-------------------------------------------------------------
		private const string nameFramesColor = "FramesColor";
		public static readonly BindableProperty FramesColorProperty = BindableProperty.Create(nameFramesColor, typeof(Color), typeof(ViewportFrames),
			Colors.Red,
			propertyChanged: RedrawCanvas); //, BindingMode.TwoWay
		public Color FramesColor
		{
			get { return (Color)GetValue(FramesColorProperty); }
			set { SetValue(FramesColorProperty, value); }
		}

		//-------------------------------------------------------------
		// AnnotationColor
		//-------------------------------------------------------------
		private const string nameAnnotationColor = "AnnotationColor";
		public static readonly BindableProperty AnnotationColorProperty = BindableProperty.Create(nameAnnotationColor, typeof(Color), typeof(ViewportFrames),
			Colors.Red,
			propertyChanged: RedrawCanvas); //, BindingMode.TwoWay
		public Color AnnotationColor
		{
			get { return (Color)GetValue(AnnotationColorProperty); }
			set { SetValue(AnnotationColorProperty, value); }
		}

		//-------------------------------------------------------------
		// AnnotationBackgroundColor
		//-------------------------------------------------------------
		private const string nameAnnotationBackgroundColor = "AnnotationBackgroundColor";
		public static readonly BindableProperty AnnotationBackgroundColorProperty = BindableProperty.Create(nameAnnotationBackgroundColor, typeof(Color), typeof(ViewportFrames),
			Colors.Transparent,
			propertyChanged: RedrawCanvas); //, BindingMode.TwoWay

		public SKTypeface TypeFace { get; set; }

		public Color AnnotationBackgroundColor
		{
			get { return (Color)GetValue(AnnotationBackgroundColorProperty); }
			set { SetValue(AnnotationBackgroundColorProperty, value); }
		}
		////-------------------------------------------------------------
		//// ClipOperation
		////-------------------------------------------------------------
		//private const string nameClipOperation = "ClipOperation";
		//public static readonly BindableProperty ClipOperationProperty = BindableProperty.Create(nameClipOperation, typeof(SKClipOperation), typeof(ViewportFrames),
		//    SKClipOperation.Difference,
		//    propertyChanged: RedrawCanvas);
		//public SKClipOperation ClipOperation
		//{
		//    get { return (SKClipOperation)GetValue(ClipOperationProperty); }
		//    set { SetValue(ClipOperationProperty, value); }
		//}




		#endregion

		protected override void OnPropertyChanged([CallerMemberName]string propertyName = null)
		{
			base.OnPropertyChanged(propertyName);

			if (propertyName == "Width"
				|| propertyName == "Height"
				|| propertyName == "ViewportFocalLength"
				|| propertyName == nameSensorRotation)
			{
				//Debug.WriteLine($"[FRAMES] Rotation: {SensorRotation}");

				Update();
				return;
			}

			if (propertyName == "FontFamily")
			{
				UpdateFont();
				return;
			}

		}

		public void Update()
		{
            if (Width > 1 && Height > 1)
            {
                //need invalidate surface on UI thread for some platforms like iOS
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    UpdateViewport(Width, Height, ViewportFocalLength);
                });
            }
        }

		public event EventHandler OnInitialized;

		private static void RedrawCanvas(BindableObject bindable, object oldvalue, object newvalue)
		{
			var me = bindable as ViewportFrames;
            MainThread.BeginInvokeOnMainThread(() =>
            {
                me.InvalidateSurface();
            });
		}




	}
}
